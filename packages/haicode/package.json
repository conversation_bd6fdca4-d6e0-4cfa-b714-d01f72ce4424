{"name": "@ht/hai-code-cli", "version": "0.2.0-alpha.6", "description": "LangChain.js and LangGraph based implementation of Coding agent", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"hai-code": "dist/cli.js"}, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "test": "vitest", "test:coverage": "vitest --coverage", "test:langfuse": "tsx examples/langfuse-test.ts", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "files": ["dist"], "keywords": ["hai-code", "agent", "cli", "langchain", "langgraph", "ai"], "author": "<PERSON><PERSON><PERSON>", "license": "Apache-2.0", "dependencies": {"@langchain/anthropic": "^0.3.5", "@langchain/community": "^0.3.11", "@langchain/core": "^0.3.15", "@langchain/langgraph": "^0.2.34", "@langchain/langgraph-checkpoint-mongodb": "^0.1.0", "@langchain/mcp-adapters": "^0.6.0", "@langchain/openai": "^0.3.7", "chalk": "^5.5.0", "dayjs": "^1.11.13", "diff": "^7.0.0", "glob": "^10.4.5", "langchain": "^0.3.5", "langfuse-langchain": "^3.38.4", "mongodb": "^6.18.0", "uuid": "^9.0.0", "zod": "^3.22.4"}, "devDependencies": {"@types/diff": "^7.0.2", "@types/node": "^20.11.17", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "tsx": "^4.7.0", "typescript": "^5.3.3", "vitest": "^1.3.1"}}